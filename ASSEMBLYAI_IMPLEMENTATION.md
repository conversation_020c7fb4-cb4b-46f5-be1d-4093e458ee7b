# assemblyai speech-to-text implementation

## overview

this document describes the implementation of assemblyai as a new speech-to-text provider for the aizen platform dubbing system. assemblyai provides cloud-based speech recognition with advanced features like speaker diarization, language detection, and high accuracy transcription.

## features implemented

### core functionality
- **cloud-based transcription**: leverages assemblyai's powerful speech recognition api
- **automatic language detection**: can detect the source language automatically
- **speaker diarization**: identifies different speakers in the audio (when enabled)
- **high accuracy**: uses assemblyai's latest models for best transcription quality
- **test mode**: works without api key for development and testing

### advanced features
- **speaker labels**: automatic speaker identification and labeling
- **sentiment analysis**: optional sentiment analysis of transcribed text
- **entity detection**: optional named entity recognition
- **auto highlights**: optional key phrase extraction
- **punctuation and formatting**: automatic punctuation and text formatting

## files created/modified

### new files
1. **`src/audio_processing/speech_to_text_assemblyai.py`** - main assemblyai implementation
2. **`test_assemblyai_integration.py`** - integration tests
3. **`.env.example`** - environment configuration template
4. **`run_config_assemblyai.json`** - example configuration using assemblyai
5. **`ASSEMBLYAI_IMPLEMENTATION.md`** - this documentation

### modified files
1. **`src/core/command_line.py`** - added `--stt` parameter with assemblyai option
2. **`src/core/main.py`** - added assemblyai initialization logic
3. **`src/config.py`** - added stt_provider to model configuration
4. **`run_config.json`** - added stt_provider field
5. **`README.md`** - updated documentation with assemblyai examples

## usage

### command line interface

```bash
# basic usage with assemblyai
python -m src.core.main --input_file video.mp4 --target_language en --stt assemblyai

# assemblyai with speaker diarization disabled (faster)
python -m src.core.main --input_file video.mp4 --target_language ru --stt assemblyai --skip_diarization

# assemblyai with azure tts
python -m src.core.main --input_file video.mp4 --target_language uz --stt assemblyai --tts azure
```

### configuration file

```bash
# using assemblyai configuration
python -m src.core.main --config run_config_assemblyai.json
```

### environment setup

1. **get assemblyai api key**: sign up at https://www.assemblyai.com/
2. **set environment variable**:
   ```bash
   export ASSEMBLYAI_API_KEY=your_api_key_here
   ```
3. **or use .env file**: copy `.env.example` to `.env` and fill in your api key

## api key management

the implementation supports multiple ways to provide the api key:

1. **environment variable**: `ASSEMBLYAI_API_KEY`
2. **parameter**: pass `api_key` to the constructor
3. **test mode**: automatically enabled when no api key is provided

## configuration options

### assemblyai-specific settings

```python
SpeechToTextAssemblyAI(
    model_name="best",              # assemblyai model type
    api_key="your_key",             # api key (optional if env var set)
    speaker_labels=True,            # enable speaker diarization
    auto_highlights=False,          # extract key phrases
    sentiment_analysis=False,       # analyze sentiment
    entity_detection=False,         # detect entities
    language_detection=True,        # auto-detect language
)
```

### integration with existing pipeline

the assemblyai provider implements the same `SpeechToText` interface as faster-whisper, making it a drop-in replacement:

- **`load_model()`**: initializes api connection
- **`get_languages()`**: returns supported language list
- **`_transcribe()`**: transcribes audio files
- **`_get_audio_language()`**: detects audio language
- **`transcribe_audio_chunks()`**: processes utterance metadata

## advantages over faster-whisper

1. **cloud processing**: no local gpu/cpu requirements
2. **advanced features**: speaker diarization, sentiment analysis, etc.
3. **high accuracy**: state-of-the-art speech recognition models
4. **automatic updates**: always uses latest models
5. **scalability**: handles large files and concurrent requests

## considerations

1. **internet required**: requires stable internet connection
2. **api costs**: usage-based pricing (check assemblyai pricing)
3. **latency**: network latency for cloud processing
4. **privacy**: audio data sent to assemblyai servers

## testing

run the integration tests to verify everything works:

```bash
python test_assemblyai_integration.py
```

the tests verify:
- initialization with and without api keys
- language support
- transcription in test mode
- language detection
- environment variable handling

## error handling

the implementation includes robust error handling:

- **api failures**: graceful fallback to empty transcription
- **network issues**: proper error logging and recovery
- **invalid api keys**: clear warning messages
- **test mode**: automatic fallback when no api key provided

## future enhancements

potential improvements for future versions:

1. **batch processing**: support for multiple file transcription
2. **real-time streaming**: live audio transcription
3. **custom vocabulary**: domain-specific terminology
4. **confidence scores**: per-word confidence metrics
5. **webhook support**: asynchronous processing notifications

## troubleshooting

### common issues

1. **"no api key found"**: set `ASSEMBLYAI_API_KEY` environment variable
2. **"api connection failed"**: check internet connection and api key validity
3. **"transcription failed"**: verify audio file format and quality
4. **"language not supported"**: check assemblyai's supported language list

### debugging

enable debug logging to see detailed information:

```bash
python -m src.core.main --input_file video.mp4 --target_language en --stt assemblyai --log_level debug
```

## conclusion

the assemblyai integration provides a powerful alternative to local speech-to-text processing, offering advanced features and high accuracy at the cost of internet dependency and api usage fees. it's particularly useful for production deployments where accuracy and advanced features are more important than local processing.
