"""centralized run configuration schema and loader for the aizen platform.

this module defines the `RunConfig` pydantic model along with nested models that
describe each logical section of the processing pipeline. it also provides a
helper ``load_config`` that reads a json file and returns a validated
``RunConfig`` instance.

usage
-----
>>> from src.config import load_config
>>> cfg = load_config("run_config.json")
>>> print(cfg.translation.target_lang)
"""

from __future__ import annotations

from pathlib import Path
from typing import List, Optional, Literal, Union
import json

from pydantic import BaseModel, Field, ValidationError, field_validator

# ---------------------------------------------------------------------------
# input processing configuration
# ---------------------------------------------------------------------------


class SilenceDetectionConfig(BaseModel):
    """parameters controlling silence (pause) detection."""

    method: Literal["energy", "webrtc"] = Field(
        default="energy",
        description="algorithm used for silence detection",
    )
    threshold: float = Field(
        default=0.02,
        description="relative energy level below which audio is considered silence",
    )
    padding_seconds: float = Field(
        default=0.25,
        description="padding added before/after detected silences in seconds",
    )


class AudioSeparationConfig(BaseModel):
    """settings for demucs or similar source separation."""

    model: str = Field(default="htdemucs", description="model name for separation")
    stems: List[str] = Field(
        default_factory=lambda: ["vocals", "accompaniment"],
        description="list of stems to separate",
    )
    output_format: str = Field(default="wav", description="audio format for stems")
    segment_duration: float = Field(
        default=30.0, description="chunk length (s) to process at once"
    )
    overlap: float = Field(
        default=0.25, description="overlap between segments (0.0-1.0)"
    )
    shifts: int = Field(
        default=1, description="number of random shifts for better quality (1-10)"
    )
    split: bool = Field(
        default=True, description="split audio into chunks for processing"
    )
    device: Optional[str] = Field(
        default=None, description="device to use (cuda/cpu, auto-detected if None)"
    )
    jobs: int = Field(default=1, description="number of parallel jobs")
    clip_mode: Literal["rescale", "clamp"] = Field(
        default="rescale", description="how to handle clipping"
    )
    float32: bool = Field(
        default=False, description="save as 32-bit float instead of 16-bit"
    )
    int24: bool = Field(
        default=False, description="save as 24-bit int instead of 16-bit"
    )
    two_stems: Optional[str] = Field(
        default=None, description="only separate into two stems (vocals/other)"
    )
    extra_args: List[str] = Field(
        default_factory=list, description="additional cli args for demucs"
    )


class InputProcessingConfig(BaseModel):
    """configuration for handling the input media and preprocessing."""

    source_path: str = Field(
        description="path to input video or audio file (local path or url)"
    )
    source_format: str = Field(default="mp4", description="container / format name")
    temp_dir: Path = Field(
        default=Path("./tmp"), description="directory for temp files"
    )
    silence: SilenceDetectionConfig = SilenceDetectionConfig()
    separation: AudioSeparationConfig = AudioSeparationConfig()

    @field_validator("source_path")
    @classmethod
    def validate_source_path(cls, v: str) -> str:
        """validate source path - keep urls as strings, validate local paths exist"""
        if v.startswith(("http://", "https://", "ftp://", "ftps://")):
            return v  # return url as-is

        # for local paths, validate they exist
        path = Path(v)
        if not path.exists():
            raise ValueError(f"local file path does not exist: {v}")
        return str(path)  # return as string to avoid path conversion issues


# ---------------------------------------------------------------------------
# translation pipeline configuration
# ---------------------------------------------------------------------------


class TranslationModelLayer(BaseModel):
    provider: Literal["google", "gemini", "yandex"]
    model: str
    temperature: float = 0.2
    top_p: float = 0.95


class TranslationConfig(BaseModel):
    source_lang: str = "en"
    target_lang: str = "es"
    analyzer: TranslationModelLayer
    segment_processor: TranslationModelLayer


# ---------------------------------------------------------------------------
# performance and resource management
# ---------------------------------------------------------------------------


class PerformanceConfig(BaseModel):
    max_workers: int = 4
    rate_limit_per_sec: int = 2
    use_gpu: bool = True
    max_memory_gb: Optional[float] = None


# ---------------------------------------------------------------------------
# model-specific configuration
# ---------------------------------------------------------------------------


class ModelConfig(BaseModel):
    transcription_chunk_size: float = 30.0
    timestamp_window: float = 0.5
    tts_provider: Literal["yandex", "azure", "elevenlabs"] = "azure"
    tts_voice: Optional[str] = None
    temperature: float = 0.7


# ---------------------------------------------------------------------------
# output & logging configuration
# ---------------------------------------------------------------------------


class OutputConfig(BaseModel):
    output_dir: Path = Path("./outputs")
    naming_pattern: str = "{basename}_{lang}.{ext}"
    log_level: Literal["debug", "info", "warning", "error", "critical"] = "info"
    log_format: str = "%(asctime)s | %(levelname)s | %(message)s"
    video_encoder: str = "libx264"


# ---------------------------------------------------------------------------
# top-level run configuration
# ---------------------------------------------------------------------------


class RunConfig(BaseModel):
    input: InputProcessingConfig
    translation: TranslationConfig
    performance: PerformanceConfig = PerformanceConfig()
    models: ModelConfig = ModelConfig()
    output: OutputConfig = OutputConfig()


# ---------------------------------------------------------------------------
# public helper
# ---------------------------------------------------------------------------


def load_config(path: str | Path) -> RunConfig:
    """load a json configuration file and return a validated :class:`RunConfig`.

    raises
    ------
    FileNotFoundError
        if the given file is missing.
    ValidationError
        if the configuration does not conform to the schema.
    """

    path = Path(path)
    if not path.exists():
        raise FileNotFoundError(path)

    with path.open("r", encoding="utf-8") as fp:
        data = json.load(fp)

    try:
        return RunConfig.model_validate(data)
    except ValidationError as exc:
        # re-raise with clearer context while preserving original details
        raise ValidationError(f"invalid configuration: {exc}") from exc


__all__ = [
    "RunConfig",
    "load_config",
    # expose nested models for import convenience
    "InputProcessingConfig",
    "TranslationConfig",
    "PerformanceConfig",
    "ModelConfig",
    "OutputConfig",
    "SilenceDetectionConfig",
    "AudioSeparationConfig",
    "TranslationModelLayer",
]
