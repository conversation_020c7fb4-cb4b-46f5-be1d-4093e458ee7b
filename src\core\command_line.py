import argparse
from pydantic import ValidationError

WHISPER_MODEL_NAMES = [
    "distil-large-v3",
    "large-v3",
]


class NewlinePreservingHelpFormatter(argparse.HelpFormatter):
    def _split_lines(self, text, width):
        # split the text by explicit newlines first
        lines = text.splitlines()
        # then apply the default behavior for line wrapping
        wrapped_lines = []
        for line in lines:
            wrapped_lines.extend(argparse.HelpFormatter._split_lines(self, line, width))
        return wrapped_lines


class CommandLine:

    @staticmethod
    def read_parameters():
        """parses command-line arguments and runs the dubbing process."""
        parser = argparse.ArgumentParser(
            description="ai dubbing system which uses machine learning models to automatically translate and synchronize audio dialogue into different languages",
            formatter_class=NewlinePreservingHelpFormatter,
        )
        parser.add_argument(
            "--input_file",
            help="path to the input video file.",
        )
        parser.add_argument(
            "--output_directory",
            default="data/outputs/",
            help="directory to save output files.",
        )
        parser.add_argument(
            "--source_language",
            help="source language (iso 639-3)",
        )
        parser.add_argument(
            "--target_language",
            help="target language for dubbing (iso 639-3).",
        )
        parser.add_argument(
            "--hugging_face_token",
            default=None,
            help="hugging face api token.",
        )
        parser.add_argument(
            "--tts",
            type=str,
            default="yandex",
            choices=["yandex", "azure", "elevenlabs"],
            help=(
                "text to speech engine to use. choices are:\n"
                "'yandex': yandex speechkit for high-quality tts with uzbek support.\n"
                "'azure': microsoft azure cognitive services tts.\n"
                "'elevenlabs': elevenlabs ai tts with multilingual support.\n"
            ),
        )
        parser.add_argument(
            "--stt",
            type=str,
            default="faster-whisper",
            choices=["faster-whisper"],
            help="speech to text using faster-whisper's openai whisper implementation.",
        )
        parser.add_argument(
            "--vad",
            action="store_true",
            help="enable vad filter when using faster-whisper (reduces hallucinations).",
        )

        parser.add_argument(
            "--device",
            type=str,
            default="auto",
            choices=["auto", "cpu", "cuda"],
            help=(
                "device to use for ml models (auto=detect best available, cpu=force cpu, cuda=force cuda)"
            ),
        )
        parser.add_argument(
            "--cpu_threads",
            type=int,
            default=0,
            help="number of threads used for cpu inference (if is not specified uses defaults for each framework)",
        )
        parser.add_argument(
            "--clean-intermediate-files",
            action="store_true",
            help="clean intermediate files used during the dubbing process",
        )

        parser.add_argument(
            "--whisper_model",
            default="distil-large-v3",
            choices=WHISPER_MODEL_NAMES,
            help="name of the openai whisper speech to text model size to use (large-v3 for best quality, distil-large-v3 for faster processing)",
        )

        parser.add_argument(
            "--target_language_region",
            default="",
            help="for some tts you can specify the region of the language. for example, 'ru' will indicate accent from russia.",
        )

        parser.add_argument(
            "--log_level",
            default="info",
            choices=["debug", "info", "warning", "error", "critical"],
            help="set the logging level",
        )
        parser.add_argument(
            "--update",
            action="store_true",
            help="update the dubbed video produced by a previous execution with the latest changes in utterance_metadata file",
        )

        parser.add_argument(
            "--original_subtitles",
            action="store_true",
            default=False,
            help="add original subtitles as stream in the output video",
        )
        parser.add_argument(
            "--dubbed_subtitles",
            default=False,
            action="store_true",
            help="add dubbed subtitles as stream in the output video",
        )

        # add segmentation options
        parser.add_argument(
            "--enhanced_segmentation",
            default=True,
            action=argparse.BooleanOptionalAction,
            help="use enhanced segmentation with natural pause detection (on by default)",
        )

        parser.add_argument(
            "--min_silence_duration",
            type=float,
            default=0.5,
            help="minimum duration (seconds) for a silence to be considered a pause (default: 0.5)",
        )

        parser.add_argument(
            "--silence_threshold",
            type=float,
            default=-35,
            help="silence threshold in db (lower = more sensitive, default: -35)",
        )

        parser.add_argument(
            "--max_segment_duration",
            type=float,
            default=30.0,
            help="maximum duration (seconds) for any speech segment (default: 30.0)",
        )

        parser.add_argument(
            "--skip_diarization",
            action="store_true",
            help="skip speaker diarization to make preprocessing much faster (but less accurate speaker detection)",
        )

        parser.add_argument(
            "--run_id",
            help="specify a run id to continue or reference a specific dubbing run",
        )

        parser.add_argument(
            "--list_runs",
            action="store_true",
            help="list all available dubbing runs and exit",
        )

        parser.add_argument(
            "--config",
            default=None,
            help="path to unified run configuration json file. if provided, other individual flags are treated as overrides",
        )

        args = parser.parse_args()

        # check if we're just listing runs
        if args.list_runs:
            return args

        # load unified config if provided (before validation so config values can be used)
        if args.config:
            try:
                from src.config import load_config

                args.run_config = load_config(args.config)
            except (FileNotFoundError, ValidationError) as exc:
                # re-raise as parser error for consistent cli feedback
                parser.error(str(exc))
        else:
            args.run_config = None

        # validate required arguments for dubbing operations (considering config values)
        input_file_from_config = None
        target_language_from_config = None

        if args.run_config:
            input_file_from_config = (
                str(args.run_config.input.source_path)
                if hasattr(args.run_config.input, "source_path")
                else None
            )
            target_language_from_config = (
                args.run_config.translation.target_lang
                if hasattr(args.run_config.translation, "target_lang")
                else None
            )

        if not args.input_file and not input_file_from_config:
            parser.error(
                "the following arguments are required: --input_file (or specify input.source_path in config file)"
            )

        if not args.target_language and not target_language_from_config:
            parser.error(
                "the following arguments are required: --target_language (or specify translation.target_lang in config file)"
            )

        return args
