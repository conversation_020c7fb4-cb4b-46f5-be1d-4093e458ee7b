#!/usr/bin/env python3
"""
simple test script to verify assemblyai integration works correctly.
this script tests the basic functionality without requiring actual api calls.
"""

import os
import sys
import tempfile
from pathlib import Path

# add src to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.audio_processing.speech_to_text_assemblyai import SpeechToTextAssemblyAI
from src.utils.logger import logger


def test_assemblyai_initialization():
    """test that assemblyai stt can be initialized properly."""
    print("testing assemblyai initialization...")
    
    # test without api key (should work in test mode)
    stt = SpeechToTextAssemblyAI(
        model_name="best",
        device="cpu",
        api_key=None,  # this should trigger test mode
        speaker_labels=True,
        language_detection=True,
    )
    
    assert stt.api_key == "test_key", "should default to test_key when no api key provided"
    assert stt.speaker_labels == True, "speaker labels should be enabled"
    assert stt.language_detection == True, "language detection should be enabled"
    
    print("✅ assemblyai initialization test passed")


def test_assemblyai_load_model():
    """test that the load_model method works."""
    print("testing assemblyai load_model...")
    
    stt = SpeechToTextAssemblyAI(api_key=None)
    stt.load_model()
    
    assert stt.model == "assemblyai_cloud", "model should be set to cloud placeholder"
    
    print("✅ assemblyai load_model test passed")


def test_assemblyai_get_languages():
    """test that get_languages returns expected language list."""
    print("testing assemblyai get_languages...")
    
    stt = SpeechToTextAssemblyAI(api_key=None)
    languages = stt.get_languages()
    
    assert isinstance(languages, list), "should return a list"
    assert len(languages) > 0, "should return non-empty list"
    assert "eng" in languages, "should support english"
    assert "rus" in languages, "should support russian"
    assert "uzb" in languages, "should support uzbek"
    
    print(f"✅ assemblyai supports {len(languages)} languages")


def test_assemblyai_transcribe_test_mode():
    """test transcription in test mode."""
    print("testing assemblyai transcription in test mode...")
    
    stt = SpeechToTextAssemblyAI(api_key=None)
    stt.load_model()
    
    # create a dummy audio file path (won't actually be used in test mode)
    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
        temp_path = temp_file.name
    
    try:
        # test transcription (should return test message)
        result = stt._transcribe(
            vocals_filepath=temp_path,
            source_language_iso_639_1="en"
        )
        
        assert isinstance(result, str), "should return string"
        assert "test transcription" in result.lower(), "should return test message"
        
        print("✅ assemblyai transcription test mode passed")
        
    finally:
        # cleanup
        if os.path.exists(temp_path):
            os.remove(temp_path)


def test_assemblyai_language_detection_test_mode():
    """test language detection in test mode."""
    print("testing assemblyai language detection in test mode...")
    
    stt = SpeechToTextAssemblyAI(api_key=None)
    stt.load_model()
    
    # create dummy audio array
    import array
    dummy_audio = array.array('h', [0] * 1000)  # 1000 samples of silence
    
    result = stt._get_audio_language(dummy_audio)
    
    assert isinstance(result, str), "should return string"
    assert result == "eng", "should default to english in test mode"
    
    print("✅ assemblyai language detection test mode passed")


def test_assemblyai_with_environment_variable():
    """test initialization with environment variable."""
    print("testing assemblyai with environment variable...")
    
    # temporarily set environment variable
    original_key = os.environ.get("ASSEMBLYAI_API_KEY")
    os.environ["ASSEMBLYAI_API_KEY"] = "test_env_key"
    
    try:
        stt = SpeechToTextAssemblyAI()
        assert stt.api_key == "test_env_key", "should use environment variable"
        
        print("✅ assemblyai environment variable test passed")
        
    finally:
        # restore original environment
        if original_key is not None:
            os.environ["ASSEMBLYAI_API_KEY"] = original_key
        else:
            os.environ.pop("ASSEMBLYAI_API_KEY", None)


def main():
    """run all tests."""
    print("🧪 running assemblyai integration tests...\n")
    
    try:
        test_assemblyai_initialization()
        test_assemblyai_load_model()
        test_assemblyai_get_languages()
        test_assemblyai_transcribe_test_mode()
        test_assemblyai_language_detection_test_mode()
        test_assemblyai_with_environment_variable()
        
        print("\n🎉 all assemblyai integration tests passed!")
        print("\n📝 next steps:")
        print("1. set your assemblyai api key: export ASSEMBLYAI_API_KEY=your_key")
        print("2. test with real audio: python -m src.core.main --input_file video.mp4 --target_language en --stt assemblyai")
        
    except Exception as e:
        print(f"\n❌ test failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
